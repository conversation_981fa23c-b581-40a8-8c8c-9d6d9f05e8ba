const { supabaseAdmin } = require('../config/supabase');
const axios = require('axios');

/**
 * Facebook API Service with User Isolation
 * Ensures each user only accesses their own Facebook data
 */
class FacebookApiService {
  
  constructor() {
    this.apiVersion = process.env.FACEBOOK_API_VERSION || 'v18.0';
    this.baseUrl = `https://graph.facebook.com/${this.apiVersion}`;
  }

  /**
   * Get user's Facebook access token with validation
   */
  async getUserFacebookToken(userId) {
    try {
      const { data: user, error } = await supabaseAdmin
        .from('user_profiles')
        .select('facebook_access_token, facebook_token_valid, facebook_id')
        .eq('id', userId)  // ← CRITICAL: User-specific query
        .eq('facebook_token_valid', true)
        .single();

      if (error || !user?.facebook_access_token) {
        throw new Error('No valid Facebook token found for user');
      }

      return {
        accessToken: user.facebook_access_token,
        facebookId: user.facebook_id
      };
    } catch (error) {
      console.error('Get user Facebook token error:', error);
      throw new Error('Facebook token not available');
    }
  }

  /**
   * Get user's Facebook ad accounts (user-isolated)
   */
  async getUserAdAccounts(userId) {
    try {
      const { accessToken } = await this.getUserFacebookToken(userId);

      const response = await axios.get(`${this.baseUrl}/me/adaccounts`, {
        params: {
          access_token: accessToken,
          fields: 'id,name,account_status,currency,timezone_name,amount_spent,balance'
        }
      });

      // Log access for audit
      await this.logApiAccess(userId, 'ad_accounts_accessed', {
        account_count: response.data.data?.length || 0
      });

      return response.data.data || [];
    } catch (error) {
      console.error('Get user ad accounts error:', error);
      
      // Log failed access
      await this.logApiAccess(userId, 'ad_accounts_access_failed', {
        error: error.message
      });
      
      throw new Error('Failed to fetch ad accounts');
    }
  }

  /**
   * Get campaigns for specific user and ad account
   */
  async getUserCampaigns(userId, adAccountId) {
    try {
      const { accessToken } = await this.getUserFacebookToken(userId);

      // Verify user owns this ad account
      const userAdAccounts = await this.getUserAdAccounts(userId);
      const ownsAccount = userAdAccounts.some(account => account.id === adAccountId);
      
      if (!ownsAccount) {
        throw new Error('User does not have access to this ad account');
      }

      const response = await axios.get(`${this.baseUrl}/${adAccountId}/campaigns`, {
        params: {
          access_token: accessToken,
          fields: 'id,name,status,objective,created_time,updated_time'
        }
      });

      // Log access for audit
      await this.logApiAccess(userId, 'campaigns_accessed', {
        ad_account_id: adAccountId,
        campaign_count: response.data.data?.length || 0
      });

      return response.data.data || [];
    } catch (error) {
      console.error('Get user campaigns error:', error);
      
      // Log failed access
      await this.logApiAccess(userId, 'campaigns_access_failed', {
        ad_account_id: adAccountId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Get ad sets for specific user, ad account, and campaign
   */
  async getUserAdSets(userId, campaignId) {
    try {
      const { accessToken } = await this.getUserFacebookToken(userId);

      // Additional validation: verify user owns the campaign
      // This would require checking the campaign's ad account ownership
      
      const response = await axios.get(`${this.baseUrl}/${campaignId}/adsets`, {
        params: {
          access_token: accessToken,
          fields: 'id,name,status,targeting,budget_remaining,daily_budget'
        }
      });

      // Log access for audit
      await this.logApiAccess(userId, 'adsets_accessed', {
        campaign_id: campaignId,
        adset_count: response.data.data?.length || 0
      });

      return response.data.data || [];
    } catch (error) {
      console.error('Get user ad sets error:', error);
      
      // Log failed access
      await this.logApiAccess(userId, 'adsets_access_failed', {
        campaign_id: campaignId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Get ads for specific user and ad set
   */
  async getUserAds(userId, adSetId) {
    try {
      const { accessToken } = await this.getUserFacebookToken(userId);

      const response = await axios.get(`${this.baseUrl}/${adSetId}/ads`, {
        params: {
          access_token: accessToken,
          fields: 'id,name,status,creative,targeting'
        }
      });

      // Log access for audit
      await this.logApiAccess(userId, 'ads_accessed', {
        adset_id: adSetId,
        ad_count: response.data.data?.length || 0
      });

      return response.data.data || [];
    } catch (error) {
      console.error('Get user ads error:', error);
      
      // Log failed access
      await this.logApiAccess(userId, 'ads_access_failed', {
        adset_id: adSetId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Create campaign for specific user and ad account
   */
  async createUserCampaign(userId, adAccountId, campaignData) {
    try {
      const { accessToken } = await this.getUserFacebookToken(userId);

      // Verify user owns this ad account
      const userAdAccounts = await this.getUserAdAccounts(userId);
      const ownsAccount = userAdAccounts.some(account => account.id === adAccountId);
      
      if (!ownsAccount) {
        throw new Error('User does not have access to this ad account');
      }

      const response = await axios.post(`${this.baseUrl}/${adAccountId}/campaigns`, {
        ...campaignData,
        access_token: accessToken
      });

      // Log creation for audit
      await this.logApiAccess(userId, 'campaign_created', {
        ad_account_id: adAccountId,
        campaign_id: response.data.id,
        campaign_name: campaignData.name
      });

      return response.data;
    } catch (error) {
      console.error('Create user campaign error:', error);
      
      // Log failed creation
      await this.logApiAccess(userId, 'campaign_creation_failed', {
        ad_account_id: adAccountId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Get insights for user's campaigns/ads
   */
  async getUserInsights(userId, objectId, objectType = 'campaign') {
    try {
      const { accessToken } = await this.getUserFacebookToken(userId);

      const response = await axios.get(`${this.baseUrl}/${objectId}/insights`, {
        params: {
          access_token: accessToken,
          fields: 'impressions,clicks,spend,cpm,cpc,ctr,reach'
        }
      });

      // Log insights access for audit
      await this.logApiAccess(userId, 'insights_accessed', {
        object_id: objectId,
        object_type: objectType
      });

      return response.data.data || [];
    } catch (error) {
      console.error('Get user insights error:', error);
      
      // Log failed access
      await this.logApiAccess(userId, 'insights_access_failed', {
        object_id: objectId,
        object_type: objectType,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Validate user has access to specific Facebook object
   */
  async validateUserAccess(userId, objectId, objectType) {
    try {
      const { accessToken } = await this.getUserFacebookToken(userId);

      // Try to access the object - if user doesn't own it, Facebook will return error
      const response = await axios.get(`${this.baseUrl}/${objectId}`, {
        params: {
          access_token: accessToken,
          fields: 'id'
        }
      });

      return !!response.data.id;
    } catch (error) {
      // If error, user doesn't have access
      return false;
    }
  }

  /**
   * Log API access for audit trail
   */
  async logApiAccess(userId, action, data) {
    try {
      await supabaseAdmin
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: 'facebook_api_access',
          event_data: {
            action,
            ...data,
            timestamp: new Date()
          },
          created_at: new Date()
        });
    } catch (error) {
      console.error('Log API access error:', error);
    }
  }

  /**
   * Rate-limited API call wrapper
   */
  async rateLimitedCall(apiFunction, retryCount = 0) {
    const maxRetries = 3;
    const baseDelay = 1000;

    try {
      return await apiFunction();
    } catch (error) {
      // Handle rate limiting
      if (error.response?.status === 429 && retryCount < maxRetries) {
        const delay = baseDelay * Math.pow(2, retryCount);
        console.log(`Rate limited, retrying in ${delay}ms...`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.rateLimitedCall(apiFunction, retryCount + 1);
      }
      
      throw error;
    }
  }
}

// Create singleton instance
const facebookApiService = new FacebookApiService();

module.exports = facebookApiService;
